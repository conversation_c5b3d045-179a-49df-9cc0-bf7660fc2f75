# تغييرات نوع طلب البريد الإلكتروني - Email Type Changes

## 📋 ملخص التغييرات

تم تغيير شكل عرض أنواع طلبات البريد الإلكتروني من **radio buttons** إلى **checkboxes** كما طُلب.

## 🔧 التعديلات المطبقة

### 1. تعديل النموذج (`models/request/base.py`)
- إضافة حقول Boolean جديدة:
  - `email_type_new` - بريد إلكتروني جديد
  - `email_type_password_reset` - إعادة تعيين كلمة المرور
  - `email_type_2fa_reset` - إعادة تعيين المصادقة الثنائية

- الحفاظ على الحقل الأصلي `email_type` للتوافق مع الإصدارات السابقة
- إضافة دالة `_compute_email_type` لحساب القيمة بناءً على الـ checkboxes
- إضافة دالة `_onchange_email_type_checkboxes` للتعامل مع تغييرات الـ checkboxes
- **إضافة التحقق من الموافقة على الشروط:**
  - `_check_email_agreement_accepted` - constraint للتحقق عند الحفظ
  - تحديث دوال `create` و `write` للتحقق من الموافقة

### 2. تعديل سير العمل (`models/request/workflow.py`)
- **إضافة التحقق في دالة `action_submit`** لمنع إرسال الطلب بدون الموافقة على الشروط

### 3. تعديل العرض (`views/request_views.xml` و `views/email_request_views.xml`)
- تغيير عرض الحقول من `widget="radio"` إلى checkboxes عادية
- تحديث شروط إظهار اتفاقية البريد الإلكتروني لتعتمد على `email_type_new`
- **إخفاء زر Submit عند عدم الموافقة على الشروط** عند اختيار "New Email"

### 4. إضافة ملف التحويل (`data/email_type_migration.xml`)
- دالة تحويل البيانات الموجودة من النظام القديم إلى الجديد
- تحديث `__manifest__.py` لتضمين ملف التحويل

## 🎯 النتيجة

الآن عند إنشاء طلب Email Request، سيظهر للمستخدم:

✅ **قبل التعديل:**
```
○ New Email
○ Password Reset
○ Two-Factor Authentication Reset
```

✅ **بعد التعديل:**
```
☐ New Email
☐ Password Reset
☐ Two-Factor Authentication Reset
```

## 🔄 التوافق مع الإصدارات السابقة

- تم الحفاظ على الحقل الأصلي `email_type`
- البيانات الموجودة ستُحول تلقائياً عند التحديث
- جميع الوظائف الأخرى تعمل بنفس الطريقة

## 🔒 ميزات الحماية الجديدة

عند اختيار "New Email":
- **لا يمكن حفظ الطلب** بدون الموافقة على الشروط
- **لا يمكن إرسال الطلب (Submit)** بدون الموافقة على الشروط
- **زر Submit يختفي** حتى يتم قبول الشروط
- **رسائل خطأ واضحة** تظهر عند المحاولة بدون موافقة

## 📝 ملاحظات

- التغيير يؤثر فقط على **الشكل** وليس على طريقة العمل الأساسية
- اتفاقية البريد الإلكتروني تظهر فقط عند اختيار "New Email"
- جميع عمليات التحقق والموافقات تعمل كما هو متوقع
- **حماية إضافية** لضمان قراءة وقبول الشروط قبل المتابعة
